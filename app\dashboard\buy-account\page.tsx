"use client"

import { useState } from "react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { 
  CheckCircle, 
  Loader2,
  CreditCard,
  QrCode,
  Upload,
  Check,
  DollarSign,
  ChevronRight,
  Star,
  AlertCircle,
  Info,
  Shield,
  Clock,
  Users,
  TrendingUp
} from "lucide-react"
import { createOrder } from "@/lib/api"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from "next/navigation"
import { trackPurchase, trackInitiateCheckout } from "@/lib/meta-pixel"

export default function BuyAccountPage() {
  const { toast } = useToast()
  const router = useRouter()
  const [formData, setFormData] = useState({
    email: "",
    challengeType: "",
    platform: "",
    size: "",
    paymentMethod: "",
    txid: "",
    proofImage: null as File | null,
  })
  
  const [showPayment, setShowPayment] = useState(false)
  const [orderPlaced, setOrderPlaced] = useState(false)
  const [selectedPrice, setSelectedPrice] = useState<number>(0)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationErrors, setValidationErrors] = useState<{[key: string]: string}>({})

  const challengeTypes = [
    {
      value: "instant",
      label: "Instant",
      description: "Instant Funding Challenge",
      price: 0,
      popular: true,
      features: ["No evaluation", "Immediate access", "Fastest payout"],
      color: "from-yellow-400 to-orange-500",
      icon: <TrendingUp className="h-5 w-5" />
    },
    { 
      value: "hft",
      label: "HFT",
      description: "High-Frequency Trading Challenge",
      price: 0,
      popular: false,
      features: ["Unlimited trading time", "Advanced algorithms", "API trading"],
      color: "from-purple-500 to-pink-500",
      icon: <Clock className="h-5 w-5" />
    },
    { 
      value: "one-step",
      label: "One-Step",
      description: "One-Step Evaluation Challenge",
      price: 0,
      popular: false,
      features: ["Simple rules", "Quick evaluation", "Low cost"],
      color: "from-blue-500 to-cyan-500",
      icon: <Check className="h-5 w-5" />
    },
    { 
      value: "two-step",
      label: "Two-Step",
      description: "Two-Step Evaluation Challenge",
      price: 0,
      popular: false,
      features: ["Standard evaluation", "Best for beginners"],
      color: "from-green-500 to-emerald-500",
      icon: <Users className="h-5 w-5" />
    },
  ]

  const platforms = [
    { value: "mt4", label: "MetaTrader 4" },
    { value: "mt5", label: "MetaTrader 5" },
  ]

  const sizes = [
    { value: "1000", label: "$1,000" },
    { value: "3000", label: "$3,000" },
    { value: "5000", label: "$5,000" },
    { value: "10000", label: "$10,000" },
    { value: "25000", label: "$25,000" },
    { value: "50000", label: "$50,000" },
    { value: "100000", label: "$100,000" },
    { value: "200000", label: "$200,000" },
    { value: "500000", label: "$500,000" },
  ];

  const paymentMethods = [
    { 
      value: "matic", 
      label: "Matic (Polygon)", 
      network: "Polygon",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/4713/small/matic-token-icon.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "usdt-trc20", 
      label: "USDT (TRC20)", 
      network: "TRON",
      address: "TTjfvRAzpb2Uz59kJR7ngRfPSjVGqL64ex",
      logo: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=TTjfvRAzpb2Uz59kJR7ngRfPSjVGqL64ex`
    },
    { 
      value: "usdt-bep20", 
      label: "USDT (BEP20)", 
      network: "BSC",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/325/small/Tether.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "bnb", 
      label: "BNB", 
      network: "BSC",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/825/small/bnb-icon2_2x.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "sol", 
      label: "Solana", 
      network: "Solana",
      address: "5AC4kgr3nG6QMhQy7fyCvsnCqmwAvVM1m1XApe1yBmGd",
      logo: "https://assets.coingecko.com/coins/images/4128/small/solana.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=5AC4kgr3nG6QMhQy7fyCvsnCqmwAvVM1m1XApe1yBmGd`
    },
    { 
      value: "eth", 
      label: "Ethereum", 
      network: "Ethereum",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/279/small/ethereum.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
    { 
      value: "btc", 
      label: "Bitcoin", 
      network: "Bitcoin",
      address: "******************************************",
      logo: "https://assets.coingecko.com/coins/images/1/small/bitcoin.png",
      qrCode: `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=******************************************`
    },
  ]

  const validateForm = () => {
    const errors: {[key: string]: string} = {}
    
    if (!formData.email) {
      errors.email = "Email is required"
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = "Please enter a valid email address"
    }
    
    if (!formData.challengeType) {
      errors.challengeType = "Please select a challenge type"
    }
    
    if (!formData.platform) {
      errors.platform = "Please select a trading platform"
    }
    
    if (!formData.size) {
      errors.size = "Please select an account size"
    }
    
    if (!formData.paymentMethod) {
      errors.paymentMethod = "Please select a payment method"
    }
    
    if (!formData.txid) {
      errors.txid = "Transaction ID is required"
    } else if (formData.txid.length < 10) {
      errors.txid = "Please enter a valid transaction ID"
    }
    
    if (!formData.proofImage) {
      errors.proofImage = "Payment proof is required"
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Add priceTable for dynamic pricing per challenge type
  const priceTable: Record<string, { real: number[]; discount: number[] }> = {
    "instant": {
      real:      [56, 131, 231, 419, 883, 1625, 2188, 2875, 4988],
      discount:  [45, 105, 185, 335, 706, 650, 875, 1150, 1995],
    },
    "hft": {
      real:      [18, 35, 48, 80, 124, 238, 275, 463, 813],
      discount:  [14, 28, 38, 64, 99, 95, 110, 185, 325],
    },
    "one-step": {
      real:      [10, 16, 28, 48, 93, 168, 248, 363, 650],
      discount:  [8, 13, 22, 38, 74, 67, 99, 145, 260],
    },
    "two-step": {
      real:      [8, 14, 25, 33, 69, 125, 188, 313, 563],
      discount:  [6, 11, 20, 26, 55, 50, 75, 125, 225],
    },
  };
  const sizeKeys = ["1000", "3000", "5000", "10000", "25000", "50000", "100000", "200000", "500000"];

  // Add a 60% OFF sale for demonstration
  const saleActive = true;
  const salePercent = 60;
  const getDiscountedPrice = (price: number) => saleActive ? Math.round(price * (1 - salePercent / 100)) : price;
  const isPopularSize = (sizeValue: string) => sizeValue === "50000" || sizeValue === "100000";

  // Update handleInputChange to update prices when challengeType or size changes
  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear validation error when user starts typing
    if (validationErrors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: "" }))
    }
    
    if (field === "paymentMethod" && value) {
      setShowPayment(true)
      // Track initiate checkout when user selects payment method
      trackInitiateCheckout(selectedPrice, 'USD')
    }
    if (field === "challengeType") {
      // Reset size and price when challenge type changes
      setFormData(prev => ({ ...prev, size: "" }))
      setSelectedPrice(0)
    }
    if (field === "size") {
      // Use dynamic priceTable if challengeType is selected
      const idx = sizeKeys.indexOf(value)
      const challengeType = formData.challengeType || "instant"
      const priceArr = priceTable[challengeType] || priceTable["instant"]
      setSelectedPrice(idx >= 0 ? priceArr.real[idx] : 0)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type and size
      const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
      const maxSize = 5 * 1024 * 1024 // 5MB
      
      if (!allowedTypes.includes(file.type)) {
        setValidationErrors(prev => ({ ...prev, proofImage: "Please upload a valid image file (JPEG, PNG)" }))
        return
      }
      
      if (file.size > maxSize) {
        setValidationErrors(prev => ({ ...prev, proofImage: "File size must be less than 5MB" }))
        return
      }
      
      setFormData(prev => ({ ...prev, proofImage: file }))
      setValidationErrors(prev => ({ ...prev, proofImage: "" }))
    }
  }

  const handlePlaceOrder = async () => {
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors in the form",
        variant: "destructive",
      })
      return
    }

    try {
      setIsSubmitting(true)
      
      const orderData = {
        email: formData.email,
        challenge_type: formData.challengeType,
        account_size: formData.size,
        platform: formData.platform,
        payment_method: formData.paymentMethod,
        txid: formData.txid,
        image: formData.proofImage!,
      }

      await createOrder(orderData)

      // Track purchase event with Meta Pixel
      trackPurchase(selectedPrice, 'USD')

      toast({
        title: "Order Placed Successfully!",
        description: "Your order has been submitted and is being processed. You will receive an email confirmation shortly.",
        duration: 5000,
      })

      // Redirect to dashboard immediately after successful order
      router.push('/dashboard')
    } catch (error) {
      console.error('Failed to place order:', error)
      toast({
        title: "Order Failed",
        description: error instanceof Error ? error.message : "Failed to place order. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const selectedPaymentMethod = paymentMethods.find(pm => pm.value === formData.paymentMethod)
  const selectedChallengeType = challengeTypes.find(ct => ct.value === formData.challengeType)
  const selectedSize = sizes.find(s => s.value === formData.size)

  if (orderPlaced) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] flex items-center justify-center p-4">
        <Card className="w-full max-w-md bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
          <CardContent className="text-center p-8">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-white mb-4">
              Order Placed Successfully!
            </h2>
            <p className="text-gray-300 mb-6">
              Your order has been submitted and is being processed. You will receive an email confirmation shortly.
            </p>
            <Button 
              onClick={() => {
                setOrderPlaced(false)
                setFormData({
                  email: "",
                  challengeType: "",
                  platform: "",
                  size: "",
                  paymentMethod: "",
                  txid: "",
                  proofImage: null,
                })
                setSelectedPrice(0)
                setShowPayment(false)
                setValidationErrors({})
              }}
              className="w-full bg-gradient-to-r from-sky-500 to-blue-600 hover:from-sky-600 hover:to-blue-700 text-white"
            >
              Place Another Order
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#002a3c] via-[#003a4c] to-[#001a2c] text-white">
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Header */}
        <div className="mb-10">
          <div className="text-center mb-6">
            <h1 className="text-4xl md:text-5xl font-extrabold bg-gradient-to-r from-sky-400 to-blue-600 bg-clip-text text-transparent mb-2 drop-shadow-lg">Buy Your Account</h1>
            <p className="text-gray-300 text-lg">Get started in three easy steps</p>
          </div>
          <div className="flex justify-center items-center gap-4 mb-8">
            {["Select Challenge", "Configure Account", "Payment"].map((step, i) => (
              <div key={step} className="flex items-center">
                <div className={`rounded-full w-10 h-10 flex items-center justify-center font-bold text-lg ${i === 0 ? 'bg-sky-500 text-white' : 'bg-gray-800 text-gray-400'} shadow-lg`}>{i + 1}</div>
                <span className={`ml-2 mr-4 font-semibold ${i === 0 ? 'text-sky-400' : 'text-gray-400'}`}>{step}</span>
                {i < 2 && <div className="w-8 h-1 bg-gradient-to-r from-sky-400 to-blue-400 rounded-full" />}
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Challenge Type Selection */}
            <Card className="bg-transparent border-0 shadow-none">
              <CardHeader>
                <CardTitle className="text-xl text-white">Select Challenge Type</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-6 justify-center">
                  {challengeTypes.map((type) => (
                    <div
                      key={type.value}
                      className={`relative flex flex-col items-center justify-center w-56 h-40 rounded-2xl cursor-pointer transition-all duration-300 shadow-xl bg-gradient-to-br ${type.value === formData.challengeType ? 'from-yellow-400 to-orange-400 scale-105 border-4 border-yellow-300' : 'from-[#001a2c] to-[#003a4c] border-2 border-sky-900/60 hover:border-sky-400/80 hover:scale-105'}`}
                      onClick={() => handleInputChange("challengeType", type.value)}
                    >
                      {type.popular && (
                        <span className="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow">POPULAR</span>
                      )}
                      <div className="mb-2 text-3xl">{type.icon}</div>
                      <div className="font-bold text-lg text-white mb-1">{type.label}</div>
                      <div className="text-sm text-gray-200 text-center px-2">{type.description}</div>
                    </div>
                  ))}
                </div>
                {validationErrors.challengeType && (
                  <p className="text-red-400 text-sm mt-2 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {validationErrors.challengeType}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Account Configuration */}
            <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
              <CardHeader>
                <CardTitle className="text-xl text-white">Account Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <Label htmlFor="email" className="text-sm font-medium text-gray-300 mb-2 block">
                      Email Address
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange("email", e.target.value)}
                      className={`bg-[#001a2c]/50 border-sky-900/60 text-white placeholder-gray-400 focus:border-sky-400 focus:ring-sky-400 ${
                        validationErrors.email ? 'border-red-500' : ''
                      }`}
                      placeholder="Enter your email"
                    />
                    {validationErrors.email && (
                      <p className="text-red-400 text-sm mt-2 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {validationErrors.email}
                      </p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="platform" className="text-sm font-medium text-gray-300 mb-2 block">
                      Trading Platform
                    </Label>
                    <Select value={formData.platform} onValueChange={(value) => handleInputChange("platform", value)}>
                      <SelectTrigger className={`bg-[#001a2c]/50 border-sky-900/60 text-white focus:border-sky-400 focus:ring-sky-400 ${
                        validationErrors.platform ? 'border-red-500' : ''
                      }`}>
                        <SelectValue placeholder="Select platform" />
                      </SelectTrigger>
                      <SelectContent className="bg-gradient-to-br from-[#001a2c] via-[#002a3c] to-[#003a4c] border-sky-900/60">
                        {platforms.map((platform) => (
                          <SelectItem key={platform.value} value={platform.value} className="text-white hover:bg-sky-500/20">
                            {platform.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {validationErrors.platform && (
                      <p className="text-red-400 text-sm mt-2 flex items-center">
                        <AlertCircle className="h-4 w-4 mr-1" />
                        {validationErrors.platform}
                      </p>
                    )}
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-300 mb-4 block">
                    Account Size
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mt-6 mb-8">
                    {formData.challengeType ? (
                      sizes.map((size, idx) => {
                        const challengeType = formData.challengeType
                        const priceArr = priceTable[challengeType] || priceTable["instant"]
                        const realPrice = priceArr.real[idx]
                        const discountedPrice = priceArr.discount[idx]
                        return (
                          <div
                            key={size.value}
                            className={`relative flex flex-col items-center justify-center rounded-full p-6 cursor-pointer transition-all duration-300 shadow-xl bg-gradient-to-br ${formData.size === size.value ? 'from-yellow-400 to-orange-400 scale-105 border-4 border-yellow-300' : 'from-[#001a2c] to-[#003a4c] border-2 border-sky-900/60 hover:border-sky-400/80 hover:scale-105'}`}
                            onClick={() => handleInputChange("size", size.value)}
                            style={{ minHeight: 110 }}
                          >
                            {isPopularSize(size.value) && (
                              <span className="absolute top-3 right-3 bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow">BEST VALUE</span>
                            )}
                            {saleActive && (
                              <span className="absolute top-3 left-3 bg-gradient-to-r from-sky-400 to-blue-400 text-white text-xs font-bold px-2 py-0.5 rounded-full shadow">60% OFF</span>
                            )}
                            <div className="font-bold text-lg text-white mb-1">{size.label}</div>
                            <div className="text-sm text-gray-400 line-through">${realPrice}</div>
                            <div className="text-2xl font-extrabold text-yellow-300">${discountedPrice}</div>
                          </div>
                        )
                      })
                    ) : (
                      <div className="col-span-4 text-center text-yellow-300 font-semibold py-8">Please select a challenge type to see prices.</div>
                    )}
                  </div>
                  {validationErrors.size && (
                    <p className="text-red-400 text-sm mt-2 flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {validationErrors.size}
                    </p>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Order Summary */}
            <div className="sticky top-8">
              <Card className="bg-gradient-to-br from-[#001a2c] via-[#003a4c] to-[#002a3c] border-2 border-yellow-400 shadow-2xl rounded-2xl">
                <CardHeader>
                  <CardTitle className="text-xl text-yellow-300 flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-yellow-300" />
                    Order Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {selectedChallengeType && (
                    <div className="flex justify-between items-center p-4 bg-gradient-to-br from-[#001a2c]/60 to-[#002a3c]/60 rounded-lg border border-sky-900/60">
                      <div>
                        <div className="font-medium text-white">{selectedChallengeType.label}</div>
                        <div className="text-sm text-gray-400">{selectedChallengeType.description}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-xl font-bold text-yellow-300">${selectedPrice}</div>
                        <div className="text-xs text-gray-500">Setup Fee</div>
                      </div>
                    </div>
                  )}
                  <div className="border-t border-sky-900/60 pt-4">
                    <div className="flex justify-between items-center">
                      <span className="text-lg font-semibold text-white">Total</span>
                      <span className="text-2xl font-bold text-yellow-300">
                        ${selectedPrice}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Payment Method */}
            <Card className="bg-transparent border-0 shadow-none mt-8">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-green-400" />
                  Payment Method
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-6 justify-center">
                  {paymentMethods.map((method) => (
                    <div
                      key={method.value}
                      className={`relative flex flex-col items-center justify-center w-44 h-32 rounded-2xl cursor-pointer transition-all duration-300 shadow-xl bg-gradient-to-br ${formData.paymentMethod === method.value ? 'from-green-400 to-green-600 scale-105 border-4 border-green-300' : 'from-[#001a2c] to-[#003a4c] border-2 border-sky-900/60 hover:border-green-400/80 hover:scale-105'}`}
                      onClick={() => handleInputChange("paymentMethod", method.value)}
                    >
                      <img src={method.logo} alt={method.label} className="w-10 h-10 rounded-full mb-2" />
                      <div className="font-semibold text-white text-sm mb-1">{method.label}</div>
                      <Badge variant="secondary" className="mt-1 bg-sky-500/20 text-sky-300 border-sky-500/40 text-xs">
                        {method.network}
                      </Badge>
                    </div>
                  ))}
                </div>
                {validationErrors.paymentMethod && (
                  <p className="text-red-400 text-sm mt-2 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {validationErrors.paymentMethod}
                  </p>
                )}
              </CardContent>
            </Card>

            {/* Payment Details */}
            {showPayment && selectedPaymentMethod && (
              <Card className="bg-gradient-to-br from-[#001a2c]/90 via-[#002a3c]/90 to-[#003a4c]/90 backdrop-blur-xl border border-sky-900/60">
                <CardHeader>
                  <CardTitle className="text-xl text-white flex items-center gap-2">
                    <QrCode className="h-5 w-5 text-orange-400" />
                    Payment Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="bg-white p-4 rounded-lg border inline-block">
                      <img 
                        src={selectedPaymentMethod.qrCode}
                        alt="QR Code"
                        className="w-32 h-32"
                      />
                    </div>
                    <div className="mt-4 p-4 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 rounded-lg border border-sky-900/60">
                      <div className="text-2xl font-bold text-white">
                        ${selectedPrice}
                      </div>
                      <div className="text-sm text-gray-400">Amount to Pay</div>
                    </div>
                  </div>
                  
                  <div className="space-y-4">
                    <div>
                      <Label className="text-sm font-medium text-gray-300 mb-2 block">
                        Wallet Address
                      </Label>
                      <div className="p-3 bg-gradient-to-br from-[#001a2c]/50 to-[#002a3c]/50 rounded-lg border border-sky-900/60">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-300 font-mono break-all">
                            {selectedPaymentMethod.address}
                          </span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {
                              navigator.clipboard.writeText(selectedPaymentMethod.address)
                              toast({
                                title: "Address Copied",
                                description: "Wallet address copied to clipboard",
                                duration: 2000,
                              })
                            }}
                            className="text-sky-400 hover:text-sky-300 ml-2"
                          >
                            Copy
                          </Button>
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-300 mb-2 block">
                        Transaction ID (TXID)
                      </Label>
                      <Input
                        value={formData.txid}
                        onChange={(e) => handleInputChange("txid", e.target.value)}
                        className={`bg-[#001a2c]/50 border-sky-900/60 text-white placeholder-gray-400 focus:border-sky-400 focus:ring-sky-400 ${
                          validationErrors.txid ? 'border-red-500' : ''
                        }`}
                        placeholder="Enter transaction ID"
                      />
                      {validationErrors.txid && (
                        <p className="text-red-400 text-sm mt-2 flex items-center">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          {validationErrors.txid}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <Label className="text-sm font-medium text-gray-300 mb-2 block">
                        Payment Proof
                      </Label>
                      <div>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleFileUpload}
                          className={`bg-[#001a2c]/50 border-sky-900/60 text-white file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-sky-600 file:text-white hover:file:bg-sky-700 ${
                            validationErrors.proofImage ? 'border-red-500' : ''
                          }`}
                        />
                      </div>
                      {formData.proofImage && (
                        <p className="text-green-400 text-sm mt-2 flex items-center">
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {formData.proofImage.name}
                        </p>
                      )}
                      {validationErrors.proofImage && (
                        <p className="text-red-400 text-sm mt-2 flex items-center">
                          <AlertCircle className="h-4 w-4 mr-1" />
                          {validationErrors.proofImage}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Submit Button */}
            <div className="flex flex-col items-center mt-8">
              <Button 
                onClick={handlePlaceOrder}
                disabled={isSubmitting}
                className="w-full bg-gradient-to-r from-yellow-400 to-orange-400 text-white text-lg font-bold py-4 rounded-xl shadow-xl hover:scale-105 transition-all relative overflow-hidden shine-btn"
                size="lg"
              >
                <span className="relative z-10 flex items-center justify-center gap-2">
                  <Shield className="mr-2 h-5 w-5" />
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Processing Order...
                    </>
                  ) : (
                    <>Proceed to Payment</>
                  )}
                </span>
                <span className="absolute left-0 top-0 w-full h-full pointer-events-none animate-shine" />
              </Button>
              <div className="flex items-center gap-2 mt-2">
                <Shield className="h-4 w-4 text-green-400" />
                <span className="text-green-300 text-xs font-bold">Secure Payment</span>
              </div>
            </div>

            {/* Security Notice */}
            <Card className="bg-gradient-to-br from-[#001a2c]/50 via-[#002a3c]/50 to-[#003a4c]/50 backdrop-blur-xl border border-sky-900/60">
              <CardContent className="p-4">
                <div className="flex items-start space-x-3">
                  <Info className="h-5 w-5 text-sky-400 mt-0.5" />
                  <div className="text-sm text-gray-300">
                    <p className="font-medium text-white mb-1">Secure Payment</p>
                    <p>Your payment information is encrypted and secure. We use industry-standard security protocols to protect your data.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
      <style jsx global>{`
        .shine-btn .animate-shine {
          background: linear-gradient(120deg, transparent 0%, #fff8 40%, #fff0 60%, transparent 100%);
          transform: translateX(-100%);
          animation: shine-move 2.5s linear infinite;
        }
        @keyframes shine-move {
          0% { transform: translateX(-100%); }
          100% { transform: translateX(100%); }
        }
      `}</style>
    </div>
  )
}
