"use client"

import type React from "react"

import { useState } from "react"
import { motion } from "framer-motion"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"


import { AlertCircle, ArrowRight, CheckCircle, Clock, DollarSign, FileText, HelpCircle } from "lucide-react"


export default function WithdrawalsPage() {
  const fadeInUp = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0, transition: { duration: 0.5 } },
  }

  return (
    <div className="space-y-8">
      {/* KYC Requirement Card */}
      <motion.div variants={fadeInUp} initial="hidden" animate="visible">
        <Card className="bg-gradient-to-r from-blue-500/10 to-teal-500/10 border-blue-500/30">
          <CardHeader>
            <div className="flex items-center">
              <FileText className="h-6 w-6 text-blue-500 mr-3" />
              <div>
                <CardTitle className="text-white">KYC Verification Required</CardTitle>
                <CardDescription className="text-gray-300">
                  Complete KYC verification to enable withdrawals
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-white font-medium">Identity Verification Required</p>
                  <p className="text-gray-400 text-sm">
                    Complete your KYC (Know Your Customer) verification by providing valid identification documents and personal information.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-white font-medium">Secure Withdrawal Process</p>
                  <p className="text-gray-400 text-sm">
                    KYC verification ensures secure withdrawals and compliance with financial regulations, protecting both you and our platform.
                  </p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                <div>
                  <p className="text-white font-medium">One-Time Process</p>
                  <p className="text-gray-400 text-sm">
                    Complete KYC verification once, and you'll have access to withdrawals for all your current and future funded accounts.
                  </p>
                </div>
              </div>
            </div>
            <div className="mt-6 flex flex-col sm:flex-row gap-3">
              <Button className="bg-blue-500 hover:bg-blue-600 text-white flex-1">
                <FileText className="h-4 w-4 mr-2" />
                Complete KYC Verification
              </Button>
              <Button variant="outline" className="border-[#003a4c] text-white hover:bg-[#003a4c] flex-1">
                <HelpCircle className="h-4 w-4 mr-2" />
                Learn More
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>






        <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.2 }}>
          <Card className="bg-[#002a3c] border-[#003a4c]">
            <CardHeader>
              <CardTitle className="text-white">Withdrawal Info</CardTitle>
              <CardDescription className="text-gray-400">Important information about withdrawals</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <div className="flex items-center">
                  <Clock className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Processing Time</h3>
                </div>
                <p className="text-sm text-gray-400">
                  Withdrawals are processed within 24 hours on business days. Bank transfers may take 2-5 business days
                  to reflect in your account.
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <DollarSign className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Withdrawal Limits</h3>
                </div>
                <p className="text-sm text-gray-400">
                  Minimum withdrawal: $100
                  <br />
                  Maximum withdrawal: Your available balance
                  <br />
                  Monthly limit: $50,000
                </p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Requirements</h3>
                </div>
                <div className="space-y-2">
                  <p className="text-sm text-gray-400">
                    <span className="text-blue-400 font-medium">✓ Complete KYC Verification:</span> Identity verification is mandatory before your first withdrawal.
                  </p>
                  <p className="text-sm text-gray-400">
                    <span className="text-blue-400 font-medium">✓ Pass Trading Challenge:</span> Successfully complete your chosen trading challenge.
                  </p>
                  <p className="text-sm text-gray-400">
                    <span className="text-blue-400 font-medium">✓ Account Verification:</span> Withdrawals are only processed to accounts in your name.
                  </p>
                  <p className="text-sm text-gray-400">
                    <span className="text-blue-400 font-medium">✓ Minimum Balance:</span> Maintain minimum withdrawal amount of $100.
                  </p>
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center">
                  <HelpCircle className="h-5 w-5 text-teal-500 mr-2" />
                  <h3 className="text-white font-medium">Need Help?</h3>
                </div>
                <p className="text-sm text-gray-400">
                  If you have any questions about withdrawals, please contact our support team at{" "}
                  <a href="mailto:<EMAIL>" className="text-teal-400 hover:underline">
                    <EMAIL>
                  </a>
                </p>
              </div>
            </CardContent>
            <CardFooter className="border-t border-[#003a4c] pt-6">
              <Button variant="outline" className="w-full border-[#003a4c] text-white hover:bg-[#003a4c]">
                View Withdrawal Policy
              </Button>
            </CardFooter>
          </Card>
        </motion.div>
      </div>

      <motion.div variants={fadeInUp} initial="hidden" animate="visible" transition={{ delay: 0.3 }}>
        <Card className="bg-[#002a3c] border-[#003a4c]">
          <CardHeader>
            <CardTitle className="text-white">Withdrawal Status</CardTitle>
            <CardDescription className="text-gray-400">Track the status of your withdrawal requests</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-[#003a4c]"></div>
                <div className="space-y-8">
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-teal-500 flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-white" />
                    </div>
                    <h3 className="text-white font-medium">Request Submitted</h3>
                    <p className="text-sm text-gray-400 mt-1">
                      Your withdrawal request has been submitted and is awaiting processing.
                    </p>
                  </div>
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-[#003a4c] flex items-center justify-center">
                      <Clock className="h-5 w-5 text-gray-400" />
                    </div>
                    <h3 className="text-gray-400 font-medium">Processing</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      Our finance team is processing your withdrawal request.
                    </p>
                  </div>
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-[#003a4c] flex items-center justify-center">
                      <ArrowRight className="h-5 w-5 text-gray-400" />
                    </div>
                    <h3 className="text-gray-400 font-medium">Funds Sent</h3>
                    <p className="text-sm text-gray-500 mt-1">Funds have been sent to your specified payment method.</p>
                  </div>
                  <div className="relative pl-10">
                    <div className="absolute left-0 top-1 w-8 h-8 rounded-full bg-[#003a4c] flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-gray-400" />
                    </div>
                    <h3 className="text-gray-400 font-medium">Completed</h3>
                    <p className="text-sm text-gray-500 mt-1">
                      The withdrawal has been completed and funds should be available in your account.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
